using Microsoft.Extensions.Hosting;
using PlatformApp.Controller;
using PlatformApp.Scraping;

// var builder = WebApplication.CreateBuilder(args);

// builder.Services.AddControllersWithViews();

// builder.Services.AddHostedService<ScrapingService>();

// var app = builder.Build();

// app.Run();

public class Program
{
    public static async Task Main(string[] args)
    {
        var scraper = new Scraper();
        string targetUrl = "https://trafficinfo.westjr.co.jp/chugoku.html"; // スクレイピングしたいURLに置き換えてください

        Console.WriteLine($"'{targetUrl}'から商品タイトルをスクレイピング中...");

        string? res = await scraper.GetHtmlAsync(targetUrl);

        Console.WriteLine(res);
    }
}